# !/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author:   TaoXF
Date:     2022-07-11 15:30:28
Version:  Python Version 3.7.4
"""

import sys
sys.path.append("../../")
from utils.user_logbook import UserLogbook

logger = UserLogbook().logger
from ziru_zuling.ziru_zuling.run_all import ZiRu<PERSON>pider
from ziru_zuling.ziru_zuling.run_details import ZiRuDetails
import urllib3
urllib3.disable_warnings()
from apscheduler.schedulers.background import BackgroundScheduler
import time
from utils.config_mgr import ConfigMgr



def get_details_info():
    try:
        zr = ZiRuDetails()
        zr.control_sync_work([15, 20])
    except Exception as err:
        logger.error("开启详情任务异常：" + str(err))


def get_all_url_info():
    try:
        zr = ZiRuSpider()
        zr.get_url_list()
    except Exception as err:
        logger.error("开启列表任务异常：" + str(err))


def do_sync_job():
    sched = BackgroundScheduler(timezone='Asia/Shanghai')
    spider_env = ConfigMgr.get("house_data_envs")
    if spider_env == "pro":
        ha = "3"
        hd = "10"
    else:
        ha = "2"
        hd = "9"
    sched.add_job(get_all_url_info, trigger='cron', day_of_week='*', hour=ha, minute='0', second='0', id='get_all_url_info', misfire_grace_time=7200)
    sched.add_job(get_details_info, trigger='cron', day_of_week='*', hour=hd, minute='0', second='0', id='get_details_info', misfire_grace_time=7200)
    sched.start()


def main():
    do_sync_job()
    count_ = 0
    logger.info("ZiRu Spider Is Beginning")
    while True:
        count_ += 1
        if count_ >= 60:
            count_ = 0
            logger.info("ZiRu Spider Is Running......")
        time.sleep(1)


if __name__ == '__main__':
    main()