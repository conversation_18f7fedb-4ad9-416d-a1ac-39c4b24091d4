#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
内存监控脚本
用于监控爬虫进程的内存使用情况
"""

import psutil
import time
import sys
import os
from datetime import datetime

def monitor_memory(process_name="python", max_memory_mb=2048, check_interval=60):
    """
    监控指定进程的内存使用
    
    Args:
        process_name: 进程名称
        max_memory_mb: 最大内存限制(MB)
        check_interval: 检查间隔(秒)
    """
    print("=" * 60)
    print("🔍 内存监控器启动")
    print("进程名称: {}".format(process_name))
    print("内存限制: {} MB".format(max_memory_mb))
    print("检查间隔: {} 秒".format(check_interval))
    print("=" * 60)
    
    while True:
        try:
            # 查找目标进程
            target_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'cmdline']):
                try:
                    if process_name.lower() in proc.info['name'].lower():
                        # 检查是否是爬虫相关进程
                        cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                        if 'run_all.py' in cmdline or 'run_details.py' in cmdline:
                            target_processes.append(proc)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            if not target_processes:
                print("[{}] ⚠️  未找到目标进程".format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
                time.sleep(check_interval)
                continue
            
            # 监控每个进程
            for proc in target_processes:
                try:
                    memory_info = proc.memory_info()
                    memory_mb = memory_info.rss / 1024 / 1024
                    
                    status = "✅ 正常"
                    if memory_mb > max_memory_mb:
                        status = "🚨 超限"
                        print("[{}] {} PID:{} 内存:{:.2f}MB - {}".format(
                            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                            status, proc.pid, memory_mb, "建议重启进程"
                        ))
                        
                        # 可选：自动重启进程（谨慎使用）
                        # restart_process(proc)
                    else:
                        print("[{}] {} PID:{} 内存:{:.2f}MB".format(
                            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                            status, proc.pid, memory_mb
                        ))
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            time.sleep(check_interval)
            
        except KeyboardInterrupt:
            print("\n🛑 监控器停止")
            break
        except Exception as e:
            print("[{}] ❌ 监控异常: {}".format(
                datetime.now().strftime("%Y-%m-%d %H:%M:%S"), e
            ))
            time.sleep(check_interval)

def restart_process(proc):
    """
    重启进程（谨慎使用）
    """
    try:
        cmdline = proc.cmdline()
        print("🔄 准备重启进程: {}".format(' '.join(cmdline)))
        
        # 终止进程
        proc.terminate()
        proc.wait(timeout=10)
        
        # 重新启动进程
        import subprocess
        subprocess.Popen(cmdline, cwd=proc.cwd())
        print("✅ 进程重启成功")
        
    except Exception as e:
        print("❌ 进程重启失败: {}".format(e))

def get_system_memory_info():
    """获取系统内存信息"""
    memory = psutil.virtual_memory()
    print("💾 系统内存信息:")
    print("  总内存: {:.2f} GB".format(memory.total / 1024 / 1024 / 1024))
    print("  可用内存: {:.2f} GB".format(memory.available / 1024 / 1024 / 1024))
    print("  使用率: {:.1f}%".format(memory.percent))
    print()

if __name__ == '__main__':
    # 显示系统内存信息
    get_system_memory_info()
    
    # 启动监控
    try:
        # 可以通过命令行参数自定义配置
        max_memory = int(sys.argv[1]) if len(sys.argv) > 1 else 2048
        check_interval = int(sys.argv[2]) if len(sys.argv) > 2 else 60
        
        monitor_memory(
            process_name="python",
            max_memory_mb=max_memory,
            check_interval=check_interval
        )
    except KeyboardInterrupt:
        print("\n👋 监控器已停止")
    except Exception as e:
        print("❌ 启动失败: {}".format(e))
