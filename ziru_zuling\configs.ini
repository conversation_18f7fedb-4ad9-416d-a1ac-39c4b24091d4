[paths]
download_path =
tmp_path =

[chromium_options]
address = 127.0.0.1:9211
browser_path = chrome

arguments = ['--no-default-browser-check', '--disable-suggestions-ui', '--no-first-run','--disable-popup-blocking', '--hide-crash-restore-bubble', '--disable-features=PrivacySandboxSettings4','--disable-blink-features=AutomationControlled','ignore-certificate-errors','--no-sandbox','--disable-infobars','--disable-dev-shm-usage','--disable-browser-side-navigation','--disable-gpu','--disable-background-networking','--headless']

extensions = []
prefs = {'profile.default_content_settings.popups': 0, 'profile.default_content_setting_values': {'notifications': 2}}
flags = {}
load_mode = normal
user = Default
auto_port = False
system_user_path = False
existing_only = False
new_env = False

[session_options]
headers = {'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'connection': 'keep-alive', 'accept-charset': 'GB2312,utf-8;q=0.7,*;q=0.7'}

[timeouts]
base = 10
page_load = 30
script = 30

[proxies]
http =
https = 

[others]
retry_times = 3
retry_interval = 2
