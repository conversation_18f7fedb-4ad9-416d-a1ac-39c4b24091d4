# !/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author:   TaoXF
Date:     2022-06-23 16:34
Version:  Python Version 3.7.4
"""

import sys
sys.path.append("../../")
from utils.mysql_database import MySQL
from utils.user_logbook import UserLogbook
from DrissionPage import ChromiumPage, ChromiumOptions

logger = UserLogbook().logger
from utils.user_agents import androidAgents
from utils.user_hashlib import userHash
from utils.datetime_func import now_day_time
from utils.user_wx import wx_talk
import re
import execjs
from utils.user_proxy import myProxy
from utils.user_website import web as w_log
import random
import requests
import urllib3
from lxml import etree
import time
import gc
import psutil
import os
urllib3.disable_warnings()
from ziru_zuling.ziru_zuling.login_data import cookiesData
from ziru_zuling.ziru_zuling.run_details import ZiRuDetails

class ZiRuSpider:

    def __init__(self):
        self.alarm_count = 0
        self.insert_count = 0
        self.source_from = "自如"
        self.page = None
        self.tab = None

        logger.info("开始初始化ZiRuSpider...")
        try:
            logger.info("正在获取cookies...")
            self.cookie, self.user_agent = ZiRuDetails().get_cookies()
            time.sleep(2)
            # 二次获取cookie
            self.cookie, self.user_agent = ZiRuDetails().get_cookies()
            logger.info("Cookies获取成功")
        except Exception as e:
            logger.error("Error getting cookies: {}".format(e))
            self.cookie, self.user_agent = cookiesData.cookies(), androidAgents.random_list()

        # 初始化DrissionPage
        logger.info("正在初始化DrissionPage...")
        self._init_drission_page()
        logger.info("ZiRuSpider初始化完成")

    def _init_drission_page(self):
        """初始化DrissionPage浏览器"""
        try:
            import random

            # 使用随机端口避免冲突
            port = random.randint(9401, 9500)

            co = ChromiumOptions()
            # CentOS 7兼容性设置
            co.set_argument('--headless=new')
            co.set_argument('--no-sandbox')
            co.set_argument('--disable-dev-shm-usage')  # CentOS 7兼容性
            co.set_argument('--disable-gpu')  # 服务器环境兼容性
            co.set_argument('--disable-web-security')  # 避免一些安全限制
            co.set_argument('--disable-features=VizDisplayCompositor')  # 兼容性
            co.set_argument('--remote-debugging-port={}'.format(port))  # 动态端口
            co.set_argument('--disable-extensions')  # 禁用扩展
            co.set_argument('--disable-plugins')  # 禁用插件
            co.set_user_agent(self.user_agent)

            self.page = ChromiumPage(co)
            self.tab = self.page.latest_tab
            
            # 先访问页面
            self.tab.get('https://wh.ziroom.com')
            time.sleep(2)
            
            # 解析并设置cookies
            if self.cookie:
                cookie_pairs = self.cookie.split(';')
                for cookie_pair in cookie_pairs:
                    cookie_pair = cookie_pair.strip()
                    if '=' in cookie_pair:
                        key, value = cookie_pair.split('=', 1)
                        try:
                            # 修复DrissionPage cookie设置API调用
                            self.tab.set.cookies({key.strip(): value.strip()})
                        except Exception as cookie_err:
                            logger.warning("设置cookie失败 {}: {}".format(key, cookie_err))

        except Exception as e:
            logger.error("初始化DrissionPage失败: {}".format(e))
            self.page = None
            self.tab = None

    def parse_html_data(self, dom_, url_info, house_area, mysql, mid_mysql, web_hash):
        # with open('index.html', 'r', encoding="utf-8") as f:
        #     data = f.read()
        count = 0  # 新增入库数量
        un_count = 0  # 重复忽略数量
        invalid_count = 0  # 无效URL数量
        total_count = 0  # 总房源数量
        try:
            # dom = etree.HTML(data)
            base_data = dom_.xpath('//div[@class="info-box"]')
            for base_info in base_data:
                total_count += 1
                uri_info = base_info.xpath("./h5/a/@href")
                house_url = "https:" + uri_info[0] if len(uri_info) else ''
                house_url = house_url.split('?')[0]
                if not house_url:
                    invalid_count += 1
                    continue
                if 'apartment' in house_url:
                    logger.info("不符合要求的URL：" + house_url)
                    invalid_count += 1
                    continue
                # house_img_list = base_info.xpath(".//div[@class='price ']/span/@style")
                finger_data = userHash.invoke(house_url)
                check_sql = "SELECT url FROM `finger` WHERE finger1='{}'".format(finger_data)
                sql = "INSERT INTO `finger`(`url`, `web`, `region`, `price`, `finger1`, `if_get`, `date1`)" \
                      " VALUES ('{}', 'ziru', '{}', NULL, '{}', NULL, '{}');".format(house_url, house_area, finger_data, now_day_time())
                check_data = mysql.query(check_sql)
                if check_data == ():
                    if mysql.exec(sql):
                        count += 1
                        self.insert_count += 1
                        logger.info("新增第{}条数据:{}".format(self.insert_count, sql))
                else:
                   un_count += 1
        except Exception as err:
            logger.error("url:{}解析入库失败:{} ".format(url_info, str(err)))
            w_log.update_sql(mid_mysql, "其他异常", web_hash, str(err))
        if total_count == 0:
            w_log.update_sql(mid_mysql, "解析异常", web_hash, "解析异常:没有解析到页面的url和房源价格信息")
        if count != 0:
            w_log.done_sql(web_hash, mid_mysql)

        # 修复日志统计，确保数据一致性
        processed_count = count + un_count  # 有效处理的数量
        logger.info("此页数据抓取解析完成,合计{}条房源信息,有效{}条(新增{}条,重复{}条),无效{}条".format(
            total_count, processed_count, count, un_count, invalid_count))


    def send_request(self, url, is_proxy):
        """使用DrissionPage获取页面数据"""
        for i in range(3):
            try:
                if not self.tab:
                    self._init_drission_page()

                if is_proxy == 1:
                    proxy_ip = myProxy.get_proxy_info(self.source_from)
                    if proxy_ip:
                        logger.info("代理信息：" + proxy_ip)
                        # DrissionPage设置代理需要在初始化时设置

                self.tab.get(url)

                # 检查页面是否加载成功
                if self.tab.url and 'ziroom.com' in self.tab.url:
                    self.alarm_count = 0
                    html_content = self.tab.html

                    # 内存优化：定期清理页面缓存
                    try:
                        self.tab.run_js('window.gc && window.gc();')  # 强制垃圾回收
                    except:
                        pass

                    return html_content
                else:
                    self.alarm_count += 1
                    logger.error("尝试第{}次获取网页异常，当前URL: {}".format(i + 1, self.tab.url))

            except Exception as err:
                self.alarm_count += 1
                logger.error("尝试第{}次获取自如列表页信息失败:{}".format(i + 1, str(err)))
                # 内存优化：异常时重新初始化浏览器
                if i == 2:  # 最后一次重试时
                    self._cleanup_browser()

            time.sleep((i + 1) * 3)

        return None

    def _check_memory_usage(self):
        """检查内存使用情况"""
        try:
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024  # 转换为MB

            logger.info("当前内存使用: {:.2f} MB".format(memory_mb))

            # 如果内存使用超过1GB，强制清理
            if memory_mb > 1024:
                logger.warning("内存使用过高({:.2f} MB)，执行清理...".format(memory_mb))
                self._cleanup_browser()
                gc.collect()  # 强制垃圾回收
                return True
            return False
        except Exception as e:
            logger.error("检查内存使用失败: {}".format(e))
            return False

    def _cleanup_browser(self):
        """清理浏览器资源"""
        try:
            if self.tab:
                self.tab.close()
            if self.page:
                self.page.quit()
        except Exception as e:
            logger.warning("清理浏览器资源时出错: {}".format(e))
        finally:
            self.page = None
            self.tab = None

    def __del__(self):
        """清理资源"""
        self._cleanup_browser()


    def parse_page_info(self, area, area_url, is_proxy_, mysql, mid_mysql):
        logger.info("获取[{}]:{}".format(area, area_url))
        web_id, web_time = w_log.web_info(self.source_from)
        web_hash = userHash.invoke(area_url + web_time)
        w_log.base_insert_sql(mid_mysql, web_id, self.source_from, web_time, web_hash, area_url, 2)
        page_data = self.send_request(area_url, is_proxy_)
        if page_data:
            dom = etree.HTML(page_data)
            page_info = dom.xpath('//*[@id="page"]/span/text()')
            page_dt = [p for p in page_info if '共' in p]
            if page_dt:
                self.parse_html_data(dom, area_url, area, mysql, mid_mysql, web_hash)
                w_log.done_sql(web_hash, mid_mysql)
                page = re.findall(r'共(\d+)页', page_dt[0], re.DOTALL)[0]
                for p in range(2, int(page) + 1):
                    sleep_time = random.randint(10, 15)
                    # 修复翻页URL构造逻辑
                    # 从 https://wh.ziroom.com/z/d420115-q1155910164886798337/?isOpen=0
                    # 构造为 https://wh.ziroom.com/z/p2-q1155910164886798337/
                    if '/?isOpen=' in area_url:
                        base_url = area_url.split('/?isOpen=')[0]  # https://wh.ziroom.com/z/d420115-q1155910164886798337
                        # 提取q参数
                        q_param = re.findall(r'-q(\d+)', base_url)[0] if '-q' in base_url else ''
                        if q_param:
                            send_url = "https://wh.ziroom.com/z/p{}-q{}/".format(p, q_param)
                        else:
                            # 备用方案：使用原来的逻辑
                            send_url = area_url[:-1] + "-p{}/".format(p)
                    else:
                        # 备用方案：使用原来的逻辑
                        send_url = area_url[:-1] + "-p{}/".format(p)
                    logger.info("获取[{}]:{}".format(area, send_url))
                    web_id_, web_time_ = w_log.web_info(self.source_from)
                    web_hash_ = userHash.invoke(send_url + web_time_)
                    w_log.base_insert_sql(mid_mysql, web_id_, self.source_from, web_time_, web_hash_, send_url, 2)
                    single_page = self.send_request(send_url, is_proxy_)
                    if single_page:
                        dom_ = etree.HTML(single_page)
                        self.parse_html_data(dom_, send_url, area, mysql, mid_mysql, web_hash_)
                    else:
                        w_log.update_sql(mid_mysql, "请求异常", web_hash_, "获取页面信息失败")

                    # 内存优化：每处理5页检查一次内存
                    if p % 5 == 0:
                        if self._check_memory_usage():
                            logger.info("内存清理完成，等待5秒后继续...")
                            time.sleep(5)

                    time.sleep(sleep_time)
                    logger.info("---" * 10)
            else:
                w_log.update_sql(mid_mysql, "请求异常", web_hash, "获取页面信息失败")
                self.alarm_count += 1
                logger.error("获取[{}]未采集到页码数:{}".format(area, area_url))
        else:
            self.alarm_count += 1
            w_log.update_sql(mid_mysql, "解析异常", web_hash, "获取基础行政区及跳转页异常，可能是网页改版")
        if self.alarm_count == 20:
            self.alarm_count = 0
            msg = "核查自如日志,失败次数连续超过20次..."
            w_log.update_sql(mid_mysql, "其他异常", web_hash, "出现异常统计次数过多")
            logger.info(msg)
            wt = wx_talk()
            wt.send(msg)
        logger.info("---" * 10)
        time.sleep(10)



    def get_url_list(self):
        web_id = "05"
        try:
            logger.info("--------------------正在获取自如房源数据---------------------")
            logger.info("正在连接数据库...")
            mysql = MySQL("spiderdb")
            mid_mysql = MySQL("mid_db")
            logger.info("数据库连接成功")
            spider_sql = "SELECT `proxy_btn`, `enable` FROM etl_hlw_cj_history_rent_config WHERE web_id='{}'".format(web_id)
            logger.info("执行查询: {}".format(spider_sql))
            spider_msg = mid_mysql.query(spider_sql)
            logger.info("查询结果: {}".format(spider_msg))
            for _msg in spider_msg:
                is_proxy = int(_msg[0])
                spider_enable = int(_msg[1])
                if spider_enable != 0:
                    url = "https://wh.ziroom.com/z/"
                    web_id, web_time = w_log.web_info(self.source_from)
                    web_hash = userHash.invoke(url + web_time)
                    w_log.base_insert_sql(mid_mysql, web_id, self.source_from, web_time, web_hash, url, 2)
                    for _ in range(3):
                        base_page = self.send_request(url, is_proxy)
                        if base_page:
                            break
                    else:
                        logger.error("获取主页信息异常")
                    if base_page:
                        dom = etree.HTML(base_page)
                        area_href_list = dom.xpath('//div[@class="wrapper"]/a/@href')
                        area_list = dom.xpath('//div[@class="wrapper"]/a/text()')
                        if len(area_href_list) == len(area_list) and len(area_list) != 0:
                            w_log.done_sql(web_hash, mid_mysql)
                            for i in range(9):
                                area = area_list[i]
                                area_url = "https:" + area_href_list[i]
                                self.parse_page_info(area, area_url, is_proxy, mysql, mid_mysql)
                                time.sleep(8)
                        else:
                            w_log.update_sql(mid_mysql, "解析异常", web_hash, "获取基础行政区及跳转页异常，可能是网页改版")
                    else:
                        w_log.update_sql(mid_mysql, "请求异常", web_hash, "获取网页信息失败")
                else:
                    logger.error("未识别的开关状态....")
        except Exception as err:
            msg = "自如租房获取列表页异常：" + str(err)
            logger.info(msg)
            wt = wx_talk()
            wt.send(msg)


if __name__ == '__main__':
    zr = ZiRuSpider()
    zr.get_url_list()
