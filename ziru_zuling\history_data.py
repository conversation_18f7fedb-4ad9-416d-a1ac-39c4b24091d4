# !/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author:   TaoXF
Date:     2022-07-12 14:22:06
Version:  Python Version 3.7.4
"""
#第一次运行处理积累数据

import sys
sys.path.append("../../")
from utils.user_logbook import UserLogbook

logger = UserLogbook().logger
from ziru_zuling.ziru_zuling.run_all import ZiRuSpider
from ziru_zuling.ziru_zuling.run_details import ZiRuDetails



def get_details_info():
    try:
        ZR = ZiRuDetails()
        ZR.control_sync_work([5, 10])
    except Exception as err:
        logger.error("开启详情任务异常：" + str(err))


def get_all_url_info():
    try:
        zr = ZiRuSpider()
        zr.get_url_list()
    except Exception as err:
        logger.error("开启列表任务异常：" + str(err))


#get_all_url_info()
get_details_info()