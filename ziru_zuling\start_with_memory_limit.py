#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
带内存限制的启动脚本
用于启动爬虫并监控内存使用
"""

import subprocess
import sys
import time
import psutil
import os
import signal
from datetime import datetime

class MemoryLimitedRunner:
    def __init__(self, script_name, max_memory_mb=2048, restart_on_exceed=True):
        self.script_name = script_name
        self.max_memory_mb = max_memory_mb
        self.restart_on_exceed = restart_on_exceed
        self.process = None
        self.restart_count = 0
        
    def start_process(self):
        """启动进程"""
        try:
            print("[{}] 🚀 启动进程: {}".format(
                datetime.now().strftime("%Y-%m-%d %H:%M:%S"), 
                self.script_name
            ))
            
            self.process = subprocess.Popen([
                sys.executable, self.script_name
            ], cwd=os.getcwd())
            
            print("[{}] ✅ 进程启动成功, PID: {}".format(
                datetime.now().strftime("%Y-%m-%d %H:%M:%S"), 
                self.process.pid
            ))
            
            return True
            
        except Exception as e:
            print("[{}] ❌ 启动失败: {}".format(
                datetime.now().strftime("%Y-%m-%d %H:%M:%S"), e
            ))
            return False
    
    def stop_process(self):
        """停止进程"""
        if self.process and self.process.poll() is None:
            try:
                print("[{}] 🛑 停止进程 PID: {}".format(
                    datetime.now().strftime("%Y-%m-%d %H:%M:%S"), 
                    self.process.pid
                ))
                
                # 优雅停止
                self.process.terminate()
                
                # 等待进程结束
                try:
                    self.process.wait(timeout=30)
                except subprocess.TimeoutExpired:
                    # 强制杀死
                    self.process.kill()
                    self.process.wait()
                
                print("[{}] ✅ 进程已停止".format(
                    datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                ))
                
            except Exception as e:
                print("[{}] ⚠️  停止进程时出错: {}".format(
                    datetime.now().strftime("%Y-%m-%d %H:%M:%S"), e
                ))
    
    def check_memory(self):
        """检查内存使用"""
        if not self.process or self.process.poll() is not None:
            return False, 0
        
        try:
            proc = psutil.Process(self.process.pid)
            memory_info = proc.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            
            return memory_mb > self.max_memory_mb, memory_mb
            
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            return False, 0
    
    def run_with_monitoring(self):
        """运行并监控"""
        print("=" * 60)
        print("🔍 内存限制运行器")
        print("脚本: {}".format(self.script_name))
        print("内存限制: {} MB".format(self.max_memory_mb))
        print("自动重启: {}".format("是" if self.restart_on_exceed else "否"))
        print("=" * 60)
        
        try:
            while True:
                # 启动进程
                if not self.start_process():
                    print("❌ 无法启动进程，退出")
                    break
                
                # 监控循环
                while True:
                    time.sleep(30)  # 每30秒检查一次
                    
                    # 检查进程是否还在运行
                    if self.process.poll() is not None:
                        print("[{}] ⚠️  进程已退出，退出码: {}".format(
                            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                            self.process.returncode
                        ))
                        break
                    
                    # 检查内存使用
                    exceed, memory_mb = self.check_memory()
                    
                    if exceed:
                        print("[{}] 🚨 内存超限: {:.2f} MB > {} MB".format(
                            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                            memory_mb, self.max_memory_mb
                        ))
                        
                        if self.restart_on_exceed:
                            self.restart_count += 1
                            print("[{}] 🔄 执行第{}次重启...".format(
                                datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                self.restart_count
                            ))
                            
                            self.stop_process()
                            time.sleep(5)  # 等待5秒后重启
                            break
                        else:
                            print("[{}] ⚠️  内存超限但未启用自动重启".format(
                                datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            ))
                    else:
                        print("[{}] ✅ 内存正常: {:.2f} MB".format(
                            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                            memory_mb
                        ))
                
                # 如果不自动重启，退出循环
                if not self.restart_on_exceed:
                    break
                    
                # 检查重启次数
                if self.restart_count >= 10:
                    print("[{}] ⚠️  重启次数过多({}次)，停止运行".format(
                        datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        self.restart_count
                    ))
                    break
        
        except KeyboardInterrupt:
            print("\n🛑 收到停止信号")
            self.stop_process()
        
        except Exception as e:
            print("[{}] ❌ 运行异常: {}".format(
                datetime.now().strftime("%Y-%m-%d %H:%M:%S"), e
            ))
            self.stop_process()

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法: python start_with_memory_limit.py <script_name> [max_memory_mb] [auto_restart]")
        print("示例: python start_with_memory_limit.py run_all.py 2048 true")
        sys.exit(1)
    
    script_name = sys.argv[1]
    max_memory_mb = int(sys.argv[2]) if len(sys.argv) > 2 else 2048
    auto_restart = sys.argv[3].lower() == 'true' if len(sys.argv) > 3 else True
    
    if not os.path.exists(script_name):
        print("❌ 脚本文件不存在: {}".format(script_name))
        sys.exit(1)
    
    runner = MemoryLimitedRunner(script_name, max_memory_mb, auto_restart)
    runner.run_with_monitoring()

if __name__ == '__main__':
    main()
