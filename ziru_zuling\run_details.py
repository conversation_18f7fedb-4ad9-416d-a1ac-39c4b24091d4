# !/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author:   TaoXF
Date:     2022-07-06 17:29:03
Version:  Python Version 3.7.4
"""

import requests
import sys
sys.path.append("../../")
from utils.mysql_database import MySQL
from utils.user_logbook import UserLogbook

logger = UserLogbook().logger
from utils.user_agents import androidAgents
from utils.user_screen_shot import webScreenShot
from utils.user_web import WEB_TOOLS
from utils.user_proxy import myProxy
from utils.datetime_func import now_time_dt
import platform
import random, re, time
import gc
import psutil
import os
from lxml import etree
from utils.config_mgr import ConfigMgr
from selenium.webdriver.common.by import By
from utils.user_wx import wx_talk
import pytesseract
import io
from PIL import Image
from utils.user_website import web as w__log
from utils.user_hashlib import userHash
from ziru_zuling.ziru_zuling.login_data import cookiesData
from DrissionPage import Chromium, ChromiumOptions, ChromiumPage


class BaseSetInfo:
    @staticmethod
    def system_info():
        sys_str = platform.system()
        if sys_str == 'Darwin':
            final_path = ConfigMgr.get("base_path_mac") + ConfigMgr.get("ziru_download_path_linux")
            TO_path = ConfigMgr.get("tesseract_path_linux")
            sys_info = 1
        elif sys_str == 'Linux':
            final_path = ConfigMgr.get("base_path_linux") + ConfigMgr.get("ziru_download_path_linux")
            TO_path = ConfigMgr.get("tesseract_path_linux")
            sys_info = 1
        elif sys_str == 'Windows':
            final_path = ConfigMgr.get("base_path_windows") + ConfigMgr.get("ziru_download_path_windows")
            TO_path = ConfigMgr.get("tesseract_path_windows")
            sys_info = 0
        else:
            final_path = ""
            TO_path = ConfigMgr.get("tesseract_path_windows")
            sys_info = 0
        return final_path, TO_path, sys_info



class ZiRuDetails:
    def __init__(self, cookie=True):
        self.source_from = '自如'
        self.alarm_count = 0
        self.sql_source = 'ziru'
        self.sleep_info = [15, 20]
        self.page = None
        self.tab = None

        if cookie:
            try:
                self.cookie, self.user_agent = self.get_cookies()
            except Exception as e:
                logger.error("Error getting cookies: {}".format(e))
                self.cookie, self.user_agent = cookiesData.cookies(), androidAgents.random_list()
        # self.cookie = cookiesData.cookies()

        # 初始化DrissionPage
        self._init_drission_page()

    def _init_drission_page(self):
        """初始化DrissionPage浏览器"""
        try:
            from DrissionPage import ChromiumPage, ChromiumOptions
            import random

            # 使用随机端口避免冲突
            port = random.randint(9223, 9300)

            co = ChromiumOptions()
            # CentOS 7兼容性设置
            co.set_argument('--headless=new')
            co.set_argument('--no-sandbox')
            co.set_argument('--disable-dev-shm-usage')  # CentOS 7兼容性
            co.set_argument('--disable-gpu')  # 服务器环境兼容性
            co.set_argument('--disable-web-security')  # 避免一些安全限制
            co.set_argument('--disable-features=VizDisplayCompositor')  # 兼容性
            co.set_argument('--remote-debugging-port={}'.format(port))  # 动态端口
            co.set_argument('--disable-extensions')  # 禁用扩展
            co.set_argument('--disable-plugins')  # 禁用插件
            if hasattr(self, 'user_agent') and self.user_agent:
                co.set_user_agent(self.user_agent)

            self.page = ChromiumPage(co)
            self.tab = self.page.latest_tab

            # 先访问页面
            self.tab.get('https://wh.ziroom.com')
            time.sleep(2)

            # 设置cookies
            if hasattr(self, 'cookie') and self.cookie:
                cookie_pairs = self.cookie.split(';')
                for cookie_pair in cookie_pairs:
                    cookie_pair = cookie_pair.strip()
                    if '=' in cookie_pair:
                        key, value = cookie_pair.split('=', 1)
                        try:
                            self.tab.set.cookies({key.strip(): value.strip()})
                        except Exception as cookie_err:
                            logger.warning("设置cookie失败 {}: {}".format(key, cookie_err))

        except Exception as e:
            logger.error("初始化DrissionPage失败: {}".format(e))
            self.page = None
            self.tab = None

    def _check_memory_usage(self):
        """检查内存使用情况"""
        try:
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024  # 转换为MB

            logger.info("当前内存使用: {:.2f} MB".format(memory_mb))

            # 如果内存使用超过1GB，强制清理
            if memory_mb > 1024:
                logger.warning("内存使用过高({:.2f} MB)，执行清理...".format(memory_mb))
                self._cleanup_browser()
                gc.collect()  # 强制垃圾回收
                return True
            return False
        except Exception as e:
            logger.error("检查内存使用失败: {}".format(e))
            return False

    def _cleanup_browser(self):
        """清理浏览器资源"""
        try:
            if self.tab:
                self.tab.close()
            if self.page:
                self.page.quit()
        except Exception as e:
            logger.warning("清理浏览器资源时出错: {}".format(e))
        finally:
            self.page = None
            self.tab = None

    def __del__(self):
        try:
            self._cleanup_browser()
        except:
            pass
        if self.alarm_count != 0:
            self.alarm_count = 0


    def get_initial_cookies(self):
        return {
            "__jsluid_s": "637d9d689d1561819dff10b9b1baecb3",
            "sensorsdata2015jssdkcross": "%7B%22distinct_id%22%3A%22192fb27b8a2a3-0f7155f47c68de-26011951-2073600-192fb27b8a3442%22%2C%22%24device_id%22%3A%22192fb27b8a2a3-0f7155f47c68de-26011951-2073600-192fb27b8a3442%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_referrer%22%3A%22%22%2C%22%24latest_referrer_host%22%3A%22%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%7D%7D",
            "Hm_lvt_4f083817a81bcb8eed537963fc1bbf10": "1744635512",
            "__jsl_clearance_s": "1745244643.048|0|cJmjlFseZ8tp5t5XsMldGmR1Gqw%3D",
            "CURRENT_CITY_CODE": "420100",
            "_csrf": "AKkpJABzWp2ZVpxb-ctah91YS4YP_Q06",
            "Hm_lpvt_4f083817a81bcb8eed537963fc1bbf10": "**********"
        }
        #     {
        #     '__jsluid_s': 'e286edbc3318fc9c1e2f274302279b6e',
        #     'CURRENT_CITY_CODE': '420100',
        #     '_csrf': 'KjuC6BwaVxN7G_AncRyOsFLLrbj3SzAj',
        #     'Hm_lvt_4f083817a81bcb8eed537963fc1bbf10': '**********',
        #     'HMACCOUNT': '96FAB210ADE8DBD4',
        #     'sensorsdata2015jssdkcross': '%7B%22distinct_id%22%3A%22192f9c18e5b74-0449f142494aa1-26011b51-2073600-192f9c18e5c2bb%22%2C%22%24device_id%22%3A%22192f9c18e5b74-0449f142494aa1-26011b51-2073600-192f9c18e5c2bb%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_referrer%22%3A%22%22%2C%22%24latest_referrer_host%22%3A%22%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%7D%7D',
        #     'visitHistory': '%5B%***********%22%2C%***********%22%5D',
        #     'CURRENT_CITY_NAME': '%E6%AD%A6%E6%B1%89',
        #     '__jsl_clearance_s': '**********.373|0|VjD%2BoyhbQ0mcVfqkdMthcrtlpPE%3D',
        #     'Hm_lpvt_4f083817a81bcb8eed537963fc1bbf10': '**********'
        # }

    def get_cookies(self):
        cookies_dict = self.get_initial_cookies()
        try:
            import random

            # 使用随机端口避免冲突
            port = random.randint(9301, 9400)

            co = ChromiumOptions()
            # CentOS 7兼容性设置
            co.set_argument('--headless=new')
            co.set_argument('--no-sandbox')  # 无沙盒模式
            co.set_argument('--disable-dev-shm-usage')  # CentOS 7兼容性
            co.set_argument('--disable-gpu')  # 服务器环境兼容性
            co.set_argument('--disable-web-security')  # 避免一些安全限制
            co.set_argument('--disable-features=VizDisplayCompositor')  # 兼容性
            co.set_argument('--remote-debugging-port={}'.format(port))  # 动态端口
            co.set_argument('--disable-extensions')  # 禁用扩展
            co.set_argument('--disable-plugins')  # 禁用插件
            co.set_user_agent(
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36")
            page = Chromium(co)
            tab = page.latest_tab
            tab.get('https://wh.ziroom.com/z/')
            new_cookies_dict = tab.cookies(all_info=True).as_dict()
            _user_agent = tab.user_agent
            logger.info("User Agent: {}".format(_user_agent))
            for key in new_cookies_dict:
                if key in cookies_dict:
                    cookies_dict[key] = new_cookies_dict[key]

            d = cookies_dict
            # 简化Cookie日志，只显示关键信息
            cookie_keys = list(d.keys())
            cookie_preview = ', '.join(cookie_keys[:3]) + ('...' if len(cookie_keys) > 3 else '')
            logger.info("Cookies获取成功，包含{}个字段: {}".format(len(cookie_keys), cookie_preview))
            parts = ["{}={}".format(k, v) for k, v in d.items()]
            cookies_str = "; ".join(parts)
            self.cookie = cookies_str
            self.user_agent = _user_agent

            with open('cookie.txt', 'w') as f:
                f.write(cookies_str)
            return cookies_str, _user_agent
        except Exception as e:
            logger.error("Error in get_cookies: {}".format(e), exc_info=True)
            # return "", ""


    def parse_mysql_data_to_pic(self, data, down_load_path, spider_env, is_proxy, mysql, wb_, wt_, t_path, sys_info, sleep_time_info, mid_mysql):
        for d in data:
            sleep_time = random.randint(sleep_time_info[0], sleep_time_info[1])
            house_dt = dict()
            url = d[0]
            region = d[1]
            finger1 = d[2]
            house_dt['WYKZMC'] = ""
            house_dt['XZQ'] = region
            house_dt['finger'] = finger1
            house_dt['LYURL'] = url
            house_dt['TJYF'] = time.strftime("%Y%m")  # 统计月份
            house_dt['CJSJ'] = time.strftime('%Y%m%d')  # 采集时间
            house_dt['A_CJR'] = 'CJR001'  # a_采集人
            house_dt['A_CJYS'] = '网络采集'  # a_采集方式
            house_dt['A_SJGY'] = self.source_from  # a_数据来源
            house_dt['A_ZLZT'] = '挂牌'  # a_租赁状态
            house_dt['SFDYT'] = ''  # 是否带阳台
            house_dt['SFDPC'] = ''  # 是否带飘窗
            house_dt['b_DH'] = ''  # 栋，单元，房号
            house_dt['b_DY'] = ''
            house_dt['b_HH'] = ''
            logger.info("请求解析url：" + url)
            web_id, web_time = w__log.web_info(self.source_from)
            web_hash = userHash.invoke(url + web_time)
            w__log.base_insert_sql(mid_mysql, web_id, self.source_from, web_time, web_hash, url, 1)
            try:
                #TODO 自如网页加载时间容易超时，需要休眠的时间比较久
                _driver_ = wt_.get_page_seconds(url, down_load_path, self.source_from, is_proxy, 40)
                # self.click_code_pass(_driver_)
                _p = etree.HTML(_driver_.page_source)
                check_result = self.check_web_exist(_p, finger1, mysql)
                if not check_result:
                    resp, chrome_driver = wb_.get_page_picture(url, _driver_)
                    if resp:
                        html_data = self.parse_html_house_details(resp, url, region, t_path, sys_info, house_dt)
                        house_area = html_data['XQMC']
                        house_price = html_data['YZJJG']
                        if house_area and house_price:
                            pic_name = wb_.down_load_picture(url, self.source_from, _driver_, down_load_path)
                            if pic_name:
                                house_dt['WYKZMC'] = pic_name
                                self.replace_into_mysql_details(house_dt, mysql, spider_env, mid_mysql, web_hash)
                                self.alarm_count = 0
                            else:
                                self.alarm_count += 1
                                w__log.update_sql(mid_mysql, "其他异常", web_hash, "其他异常:快照下载异常")
                        else:
                            logger.debug("这种链接截图出现错误,暂时做个统计,看有多少出现:" + url)
                            w__log.update_sql(mid_mysql, "解析异常", web_hash, "解析异常:页面解析失败")
                    else:
                        self.alarm_count += 1
                        w__log.update_sql(mid_mysql, "请求异常", web_hash, "请求异常:获取网页信息失败")
                else:
                    w__log.update_sql(mid_mysql, "网页删除", web_hash, "网页已被删除")
            except Exception as err:
                logger.error("自如快照下载入库异常：" + str(err))
                w__log.update_sql(mid_mysql, "其他异常", web_hash, str(err))
                self.alarm_count += 1
            time.sleep(sleep_time)
            logger.info("-------------------------------------------------------------------------------------------")
        if self.alarm_count >= 50:
            self.alarm_count = 0
            msg = "[{}]自如详情快照爬虫已连续出现50次请求实效,爬虫进程暂时停止30分钟".format(spider_env)
            w_t = wx_talk()
            w_t.send(msg)
            time.sleep(30 * 60)

    def parse_mysql_data_to_request(self, data, is_proxy, mysql, t_path, sys_info, spider_env, sleep_time_info, mid_mysql):
        for d in data:
            house_dt = dict()
            sleep_time = random.randint(sleep_time_info[0], sleep_time_info[1])
            url = d[0]
            region = d[1]
            finger1 = d[2]
            house_dt['WYKZMC'] = ""
            house_dt['XZQ'] = region
            house_dt['finger'] = finger1
            house_dt['LYURL'] = url
            house_dt['TJYF'] = time.strftime("%Y%m")  # 统计月份
            house_dt['CJSJ'] = time.strftime('%Y%m%d')  # 采集时间
            house_dt['A_CJR'] = 'CJR001'  # a_采集人
            house_dt['A_CJYS'] = '网络采集'  # a_采集方式
            house_dt['A_SJGY'] = self.source_from  # a_数据来源
            house_dt['A_ZLZT'] = '挂牌'  # a_租赁状态
            house_dt['SFDYT'] = ''  # 是否带阳台
            house_dt['SFDPC'] = ''  # 是否带飘窗
            house_dt['b_DH'] = ''  # 栋，单元，房号
            house_dt['b_DY'] = ''
            house_dt['b_HH'] = ''
            web_id, web_time = w__log.web_info(self.source_from)
            web_hash = userHash.invoke(url + web_time)
            w__log.base_insert_sql(mid_mysql, web_id, self.source_from, web_time, web_hash, url, 1)
            try:
                html_data = self.send_request(url, is_proxy, finger1, mysql)
                if html_data:
                    if html_data != 404:
                        dom_ = etree.HTML(html_data)
                        check_result = self.check_web_exist(dom_, finger1, mysql)
                        if not check_result:
                            html_dict = self.parse_html_house_details(html_data, url, region, t_path, sys_info, house_dt)
                            house_area = house_dt['XQMC']
                            house_price = house_dt['YZJJG']
                            if house_area and house_price:
                                self.replace_into_mysql_details(html_dict, mysql, spider_env, mid_mysql, web_hash)
                                self.alarm_count = 0
                            else:
                                logger.debug("小区名称为空,暂时不做出统计信息：" + url)
                                w__log.update_sql(mid_mysql, "解析异常", web_hash, "解析异常:网页解析出错")
                        else:
                            w__log.update_sql(mid_mysql, "网页删除", web_hash, "网页已被删除")
                    else:
                        w__log.update_sql(mid_mysql, "网页删除", web_hash, "网页已被删除")
                else:
                    self.alarm_count += 1
                    w__log.update_sql(mid_mysql, "请求异常", web_hash, "请求异常:获取网页信息失败或网页失效已被删除")
            except Exception as err:
                logger.error("自如网页数据解析入库异常：" + str(err))
                w__log.update_sql(mid_mysql, "其他异常", web_hash,  str(err))
            logger.info("-------------------------------------------------------------------------------------------")
            time.sleep(sleep_time)
            if self.alarm_count >= 50:
                self.alarm_count = 0
                msg = "[{}]自如详情爬虫已连续出现50次请求实效,爬虫进程暂时停止30分钟".format(spider_env)
                w_t = wx_talk()
                w_t.send(msg)
                time.sleep(30 * 60)

    @staticmethod
    def click_code_pass(_driver):
        try:
            time.sleep(3)
            button_header = _driver.find_element(By.XPATH, value="//div[@class='cover-QR-text']/img")
            _driver.execute_script("arguments[0].click();", button_header)
        except:
            pass

    def get_base_info(self, sleep_time_info):
        is_proxy = ConfigMgr.get("ziru_spider_proxy_switch_button")
        if is_proxy == 1:
            proxy_switch = "代理开启...."
        else:
            proxy_switch = "代理关闭...."
        spider_env = ConfigMgr.get("house_data_envs")
        is_screenshot = ConfigMgr.get("is_download_screenshot_{}_zulin".format(self.sql_source))
        down_load_path, t_path, sys_info = BaseSetInfo.system_info()
        mysql = MySQL("spiderdb")
        mid_mysql = MySQL("mid_db")
        if is_screenshot == 1:
            """带截图"""
            wb = webScreenShot()
            wt = WEB_TOOLS()
            logger.info("自如租赁爬虫网页快照功能开启," + proxy_switch)
            batch_count = 0  # 批次计数器
            while True:
                # TODO 解决出现未知模板的网页解析不出来导致代码死循环....
                now_time_hour = now_time_dt().hour
                if now_time_hour == 8:
                    logger.info("为了防止线程阻塞，强制在运行的爬虫在凌晨8点停止一次....")
                    return

                # 内存优化：每处理10批次检查一次内存
                batch_count += 1
                if batch_count % 10 == 0:
                    if self._check_memory_usage():
                        logger.info("内存清理完成，等待10秒后继续...")
                        time.sleep(10)

                sql = "SELECT url,region,finger1 FROM finger WHERE web='{}' and if_get is null limit 100;".format(self.sql_source)
                data = mysql.query(sql)
                if len(data) == 0:
                    logger.info("详情列表已更新完成.......")
                    break
                else:
                    self.parse_mysql_data_to_pic(data, down_load_path, spider_env, is_proxy, mysql, wb, wt, t_path, sys_info, sleep_time_info, mid_mysql)
        else:
            """不带截图"""
            logger.info("自如租赁爬虫网页快照功能关闭," + proxy_switch)
            batch_count = 0  # 批次计数器
            while True:
                # TODO 解决出现未知模板的网页解析不出来导致代码死循环....
                now_time_hour = now_time_dt().hour
                if now_time_hour == 8:
                    logger.info("为了防止线程阻塞，强制在运行的爬虫在凌晨8点停止一次....")
                    return

                # 内存优化：每处理10批次检查一次内存
                batch_count += 1
                if batch_count % 10 == 0:
                    if self._check_memory_usage():
                        logger.info("内存清理完成，等待10秒后继续...")
                        time.sleep(10)

                sql = "SELECT url, region, finger1 FROM finger WHERE web='{}' and if_get is null limit 100;".format(self.sql_source)
                data = mysql.query(sql)
                if len(data) == 0:
                    logger.info("详情列表已更新完成.......")
                    break
                else:
                    self.parse_mysql_data_to_request(data, is_proxy, mysql, t_path, sys_info, spider_env, sleep_time_info, mid_mysql)

    def control_sync_work(self, sleep_time_data):
        web_id = "05"
        spider_env = ConfigMgr.get("house_data_envs")
        down_load_path, t_path, sys_info = BaseSetInfo.system_info()
        mysql = MySQL("spiderdb")
        mid_mysql = MySQL("mid_db")
        self.do_sync_something(web_id, mysql, mid_mysql, down_load_path, spider_env, sleep_time_data, t_path, sys_info)

    @staticmethod
    def replace_into_spiderdb_msg(mysql, status_, web_id):
        sql = "UPDATE etl_hlw_cj_history_rent_config SET status='{}', update_time=NOW() WHERE web_id='{}'".format(status_, web_id)
        spider_status = "停止" if status_ == 0 else "开启"
        if mysql.exec(sql):
            logger.info("爬虫状态{}:{}".format(spider_status, sql))

    def do_sync_something(self, web_id, mysql, mid_mysql, down_load_path, spider_env, sleep_time_, t_path, sys_info):
        self.replace_into_spiderdb_msg(mid_mysql, 1, web_id)
        while True:
            now_time_hour = now_time_dt().hour
            status_sql = "SELECT `screen_btn`, `proxy_btn`, `enable` FROM etl_hlw_cj_history_rent_config WHERE web_id='{}'".format(web_id)
            status_result = mid_mysql.query(status_sql)
            is_screenshot = status_result[0][0]
            is_proxy = status_result[0][1]
            is_enable = status_result[0][2]
            if now_time_hour == 7:
                self.replace_into_spiderdb_msg(mid_mysql, 0, web_id)
                logger.info("为了防止线程阻塞，强制在运行的爬虫在凌晨7点停止....")
                return
            sql = "SELECT url, region, finger1 FROM finger WHERE web='{}' and if_get is null limit 100;".format(self.sql_source)
            data = mysql.query(sql)
            if len(data) == 0:
                self.replace_into_spiderdb_msg(mid_mysql, 0, web_id)
                logger.info("今日自如详情已全部更新完成.....")
                break
            else:
                if is_screenshot == "1" and is_proxy == "1" and is_enable == "1":
                    wb = webScreenShot()
                    wt = WEB_TOOLS()
                    logger.info("自如租赁爬虫网页快照功能开启,代理开启,快照开关使能开...")
                    self.parse_mysql_data_to_pic(data, down_load_path, spider_env, is_proxy, mysql, wb, wt, t_path, sys_info, sleep_time_, mid_mysql)
                elif is_screenshot == "1" and is_proxy == "0" and is_enable == "1":
                    wb = webScreenShot()
                    wt = WEB_TOOLS()
                    logger.info("自如租赁爬虫网页快照功能开启,代理关闭,快照开关使能开...")
                    self.parse_mysql_data_to_pic(data, down_load_path, spider_env, is_proxy, mysql, wb, wt, t_path, sys_info, sleep_time_, mid_mysql)
                elif is_screenshot == "0" and is_proxy == "1":
                    logger.info("自如租赁爬虫网页快照功能关闭,代理开启")
                    self.parse_mysql_data_to_request(data, is_proxy, mysql, t_path, sys_info, spider_env, sleep_time_, mid_mysql)
                elif is_screenshot == "0" and is_proxy == "0":
                    logger.info("自如租赁爬虫网页快照功能关闭,代理关闭")
                    self.parse_mysql_data_to_request(data, is_proxy, mysql, t_path, sys_info, spider_env, sleep_time_, mid_mysql)
                else:
                    logger.error("未识别的快照开关状态....")
                    break



    @staticmethod
    def replace_into_mysql_details(hd, mysql, spider_env, mid_mysql, web_hash):
        pic_name = hd['WYKZMC']
        if spider_env == 'dev':
            if pic_name == "":
                sql = "INSERT INTO `data_wh_wlcj2`(`ID`, `TJYF`, `CJSJ`, `A_CJR`, `A_CJYS`, `A_SJGY`, `A_ZLZT`, `LYURL`, " \
                      "`FBSJ`, `FBR`, `FBRLX`, `FGBABM`, `JGBAM`, `XQMC`, `XZQ`, `b_DH`, `b_DY`, `b_HH`, `CX`, `CH`, `ZCS`, " \
                      "`HX`, `MJ`, `ZXCD`, `JZLX`, `JGSJ`, `ZLLX`, `FKFS`, `YZJJG`, `SFDDW`, `SFDYT`, `SFDPC`, `JWD`, `WYKZMC`) " \
                      "VALUES (DEFAULT, '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', " \
                      "'{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', NULL);".\
                    format(hd['TJYF'], hd['CJSJ'], hd['A_CJR'], hd['A_CJYS'], hd['A_SJGY'], hd['A_ZLZT'], hd['LYURL'], hd['FBSJ'],
                           hd['FBR'], hd['FBRLX'], hd['FGBABM'], hd['JGBAM'], hd['XQMC'], hd['XZQ'], hd['b_DH'], hd['b_DY'],
                           hd['b_HH'], hd['CX'], hd['CH'], hd['ZCS'], hd['HX'], hd['MJ'], hd['ZXCD'], hd['JZLX'], hd['JGSJ'],
                           hd['ZLLX'], hd['FKFS'], hd['YZJJG'], hd['SFDDW'], hd['SFDYT'], hd['SFDPC'], hd['JWD'])
            else:
                sql = "INSERT INTO `data_wh_wlcj2`(`ID`, `TJYF`, `CJSJ`, `A_CJR`, `A_CJYS`, `A_SJGY`, `A_ZLZT`, `LYURL`, " \
                      "`FBSJ`, `FBR`, FBRLX, `FGBABM`, `JGBAM`, `XQMC`, `XZQ`, `b_DH`, `b_DY`, `b_HH`, `CX`, `CH`, `ZCS`, " \
                      "`HX`, `MJ`, `ZXCD`, `JZLX`, `JGSJ`, `ZLLX`, `FKFS`, `YZJJG`, `SFDDW`, `SFDYT`, `SFDPC`, `JWD`, `WYKZMC`) " \
                      "VALUES (DEFAULT, '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', " \
                      "'{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}');".\
                    format(hd['TJYF'], hd['CJSJ'], hd['A_CJR'], hd['A_CJYS'], hd['A_SJGY'], hd['A_ZLZT'], hd['LYURL'], hd['FBSJ'],
                           hd['FBR'], hd['FBRLX'], hd['FGBABM'], hd['JGBAM'], hd['XQMC'], hd['XZQ'], hd['b_DH'], hd['b_DY'],
                           hd['b_HH'], hd['CX'], hd['CH'], hd['ZCS'], hd['HX'], hd['MJ'], hd['ZXCD'], hd['JZLX'], hd['JGSJ'],
                           hd['ZLLX'], hd['FKFS'], hd['YZJJG'], hd['SFDDW'], hd['SFDYT'], hd['SFDPC'], hd['JWD'], hd['WYKZMC'])
        else:
            if pic_name == "":
                sql = "INSERT INTO `data_wh_wlcj2`(`ID`, `TJYF`, `CJSJ`, `A_CJR`, `A_CJYS`, `A_SJGY`, `A_ZLZT`, `LYURL`, " \
                      "`FBSJ`, `FBR`, `FBRLX`, `FGBABM`, `JGBAM`, `XQMC`, `XZQ`, `b_DH`, `b_DY`, `b_HH`, `CX`, `CH`, `ZCS`, " \
                      "`HX`, `MJ`, `ZXCD`, `JZLX`, `JGSJ`, `ZLLX`, `FKFS`, `YZJJG`, `SFDDW`, `SFDYT`, `SFDPC`, `WYKZMC`) " \
                      "VALUES (DEFAULT, '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', " \
                      "'{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', NULL);". \
                    format(hd['TJYF'], hd['CJSJ'], hd['A_CJR'], hd['A_CJYS'], hd['A_SJGY'], hd['A_ZLZT'], hd['LYURL'],
                           hd['FBSJ'], hd['FBR'], hd['FBRLX'], hd['FGBABM'], hd['JGBAM'], hd['XQMC'], hd['XZQ'], hd['b_DH'],
                           hd['b_DY'], hd['b_HH'], hd['CX'], hd['CH'], hd['ZCS'], hd['HX'], hd['MJ'], hd['ZXCD'], hd['JZLX'],
                           hd['JGSJ'], hd['ZLLX'], hd['FKFS'], hd['YZJJG'], hd['SFDDW'], hd['SFDYT'], hd['SFDPC'])
            else:
                sql = "INSERT INTO `data_wh_wlcj2`(`ID`, `TJYF`, `CJSJ`, `A_CJR`, `A_CJYS`, `A_SJGY`, `A_ZLZT`, `LYURL`, " \
                      "`FBSJ`, `FBR`, FBRLX, `FGBABM`, `JGBAM`, `XQMC`, `XZQ`, `b_DH`, `b_DY`, `b_HH`, `CX`, `CH`, `ZCS`, " \
                      "`HX`, `MJ`, `ZXCD`, `JZLX`, `JGSJ`, `ZLLX`, `FKFS`, `YZJJG`, `SFDDW`, `SFDYT`, `SFDPC`, `WYKZMC`) " \
                      "VALUES (DEFAULT, '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', " \
                      "'{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}');". \
                    format(hd['TJYF'], hd['CJSJ'], hd['A_CJR'], hd['A_CJYS'], hd['A_SJGY'], hd['A_ZLZT'], hd['LYURL'],
                           hd['FBSJ'], hd['FBR'], hd['FBRLX'], hd['FGBABM'], hd['JGBAM'], hd['XQMC'], hd['XZQ'], hd['b_DH'],
                           hd['b_DY'], hd['b_HH'], hd['CX'], hd['CH'], hd['ZCS'], hd['HX'], hd['MJ'], hd['ZXCD'], hd['JZLX'],
                           hd['JGSJ'], hd['ZLLX'], hd['FKFS'], hd['YZJJG'], hd['SFDDW'], hd['SFDYT'], hd['SFDPC'], hd['WYKZMC'])
        if mysql.exec(sql):
            logger.info('存储成功:{}'.format(sql))
            finger_info = hd['finger']
            update_sql = "UPDATE finger SET if_get='已保存' WHERE  finger1='{}'".format(finger_info)
            if mysql.exec(update_sql):
                logger.info("数据状态已更新：" + update_sql)
                w__log.done_sql(web_hash, mid_mysql)
            else:
                w__log.update_sql(mid_mysql, "其他异常", web_hash, "数据库入库异常,sql语句无法执行:".format(update_sql))
        else:
            w__log.update_sql(mid_mysql, "其他异常", web_hash, "数据库入库异常,sql语句无法执行:".format(sql))


    def check_web_exist(self, p, finger_info, mysql):
        res = p.xpath('//div[@class="box404"]/div[2]/text()')
        if res:
            self.delete_finger_map(finger_info, mysql)
            return True
        return False


    @staticmethod
    def parse_img(img_url, to_path, sys_info):
        # print(img_url)
        try:
            # 对于图片获取，保持使用requests，但添加错误处理
            response = requests.get(img_url, timeout=10, verify=False)
            response.raise_for_status()
            data = response.content
        except Exception as e:
            logger.error("获取图片失败: {} - {}".format(img_url, str(e)))
            return []
        pytesseract.pytesseract.tesseract_cmd = to_path
        image = Image.open(io.BytesIO(data))
        if sys_info == 0:
            vcode = pytesseract.image_to_string(image, lang='eng',
                                                config='--psm 6 --oem 3 -c tessedit_char_whitelist=0123456789').strip()  #Windows模式
        elif sys_info == 1:
            vcode = pytesseract.image_to_string(image, lang='chi_sim',
                                                config='--psm 6 --oem 3 -c tessedit_char_whitelist=0123456789').strip()  #linux或mac模式
        else:
            vcode = pytesseract.image_to_string(image, lang='eng',
                                                config='--psm 6 --oem 3 -c tessedit_char_whitelist=0123456789').strip()  # Windows模式
        vcode = ''.join(vcode.split(' '))
        vcode = re.findall('(\d+)', vcode)[0]
        ret_list = []
        for i in vcode:
            ret_list.append(i)
        return ret_list


    def get_price(self, png_url, img_list, t_path, sys_info):
        logger.info(png_url)
        if 'images/2019' in png_url:
            num = -31.24
        elif 'images/2020' in png_url:
            num = -30
        else:
            logger.info('出现第三种情况，请检查')
            msg = "自如价格识别出现第三种情况，请检查："
            logger.info(msg)
            wt = wx_talk()
            wt.send(msg)
            time.sleep(100)
            return ''
        price_list = self.parse_img(png_url, t_path, sys_info)
        price = ''
        for imgs in img_list:
            move_px_re = re.findall(r'background-position:(.*?)px', imgs, re.DOTALL)
            logger.info('{}   {}'.format(move_px_re, num))
            move_px = int(float(move_px_re[0].replace(" ", ""))/num)
            price += price_list[int(move_px)]
        logger.info(price)
        return price

    def parse_html_house_details(self, page_source, url_source, region, t_path, sys_info, dict_info):
        dom_ = etree.HTML(page_source)
        base_info = dom_.xpath('//aside/h1/text()')
        style_info = dom_.xpath('//div[@class="Z_home_info"]//dl/dd/text()')
        price_img_list_info = dom_.xpath('//div[@class="Z_price"]/i/@style')
        floor_info = dom_.xpath('//ul[@class="Z_home_o"]/li[2]/span[2]/text()')
        check_in_info = dom_.xpath('//div[@class="Z_container Z_bread mt60"]/span/text()')
        png_info = price_img_list_info[0]
        png_url = re.findall(r'background-image: url\((.*?)\)', png_info)
        png_url_re = "https:" + png_url[0]
        monthly_rent = self.get_price(png_url_re, price_img_list_info, t_path, sys_info)
        logger.info("图片解析转换的价格为：" + monthly_rent)
        house_name = base_info[0].split('·')[1]
        room_type = style_info[2]
        f = floor_info[0]
        floor, total_floor = f.split('/') if '/' in f else ['', floor_info[0]]
        room_area = style_info[0]
        lease_type = check_in_info[0][-2:]
        orientation = style_info[1]
        pay_type = "".join(dom_.xpath("//div[@class='Z_price']/span[2]/text()")).replace("/月(", "").replace(")", "")
        total_floor = int(total_floor)
        for i in range(len(room_area)):
            if room_area[i].isdigit():
                room_area = room_area[i:-1]
                room_area = float(room_area) if '.' in room_area else int(room_area)
                break
        monthly_rent = float(monthly_rent) if '.' in monthly_rent else int(monthly_rent)
        completion_date1 = dom_.xpath('//ul[@class="Z_village_info_body"]/li/span[1]/text()')
        completion_date2 = dom_.xpath('//ul[@class="Z_village_info_body"]/li/span[2]/text()')
        competion_date = completion_date2[completion_date1.index('建筑年代')] if '建筑年代' in completion_date1 else ''
        building_type = completion_date2[completion_date1.index('建筑类型')] if '建筑类型' in completion_date2 else ''
        update_time = ''
        poster = '自如'
        # id1 = dom_.xpath('//div[@class="qr_content"]/p/text()')
        # # print('id2', id2)
        # id1 = id1[0].split('：')[1] if id1 else ''
        house_code = dom_.xpath('//div[@class="qr_content"]/p/text()')
        if house_code:
            id1 = ''.join(house_code).split('：')[1]
        else:
            house_code = dom_.xpath('//div[@class="qr_content"]/a/p/text()')
            if house_code:
                id1 = ''.join(house_code).split('：')[1]
            else:
                id1 = ''
        # print('id2',id2)
        decorate = ''
        dict_info['LYURL'] = url_source
        dict_info['FBSJ'] = update_time
        dict_info['FBR'] = poster
        dict_info['FBRLX'] = '住房租赁企业'
        dict_info['FGBABM'] = id1
        dict_info['JGBAM'] = ''
        dict_info['XQMC'] = house_name
        dict_info['XZQ'] = region
        dict_info['CX'] = orientation
        dict_info['CH'] = floor
        dict_info['ZCS'] = total_floor
        dict_info['HX'] = room_type
        dict_info['MJ'] = room_area
        dict_info['JGSJ'] = competion_date
        dict_info['ZLLX'] = lease_type
        dict_info['FKFS'] = pay_type
        dict_info['YZJJG'] = monthly_rent
        dict_info['ZXCD'] = decorate
        dict_info['JZLX'] = building_type
        dict_info['SFDDW'] = ''
        dict_info['JWD'] = re.findall('"resblockPosition":\[(.*?)\]', page_source)[0]
        return dict_info


    def send_request(self, url, is_proxy, finger_dt, mysql):
        """使用DrissionPage获取页面数据"""
        for i in range(3):
            logger.info("请求解析url：" + url)
            try:
                if not self.tab:
                    self._init_drission_page()

                if is_proxy == 1:
                    proxy_ip = myProxy.get_proxy_info(self.source_from)
                    if proxy_ip:
                        logger.info("代理信息：" + proxy_ip)
                        # DrissionPage设置代理需要在初始化时设置，这里记录日志

                self.tab.get(url)

                # 检查页面是否加载成功
                if self.tab.url and 'ziroom.com' in self.tab.url:
                    # 检查是否是404页面
                    page_html = self.tab.html
                    if '404' in page_html or 'not found' in page_html.lower() or '页面不存在' in page_html:
                        self.delete_finger_map(finger_dt, mysql)
                        return 404

                    # 内存优化：强制垃圾回收
                    try:
                        self.tab.run_js('window.gc && window.gc();')  # 强制垃圾回收
                    except:
                        pass

                    self.alarm_count = 0
                    return page_html
                else:
                    self.alarm_count += 1
                    logger.error("尝试第{}次获取网页异常，当前URL: {}".format(i + 1, self.tab.url))

            except Exception as err:
                self.alarm_count += 1
                logger.error("尝试第{}次获取自如详情页信息失败:{}".format(i + 1, str(err)))

            sleep_time = random.randint(10, 20)
            time.sleep(sleep_time)

        return None

    @staticmethod
    def update_finger_map(finger__, mysql):
        sql = "UPDATE finger SET if_get='已保存' WHERE  finger1='{}'".format(finger__)
        if mysql.exec(sql):
            logger.info("更新获取标签:" + sql)

    @staticmethod
    def delete_finger_map(finger__, mysql):
        sql = "DELETE FROM finger WHERE finger1 = '{}'".format(finger__)
        if mysql.exec(sql):
            logger.info("页面未找到,删除数据库对应记录:" + sql)


    def get_base_data(self):
        self.get_base_info(self.sleep_info)

# zr = ZiRuDetails()
# zr.get_base_info()