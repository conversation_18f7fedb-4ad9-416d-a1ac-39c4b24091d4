#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试DrissionPage集成
"""

import sys
sys.path.append("../../")

from run_details import ZiRuDetails
from utils.user_logbook import UserLogbook

logger = UserLogbook().logger

def test_drission_integration():
    """测试DrissionPage集成"""
    try:
        logger.info("开始测试DrissionPage集成...")
        
        # 初始化ZiRuDetails
        zr_details = ZiRuDetails(cookie=True)
        
        # 测试发送请求
        test_url = "https://wh.ziroom.com/x/807300230.html"  # 一个示例房源URL
        logger.info("测试URL: {}".format(test_url))
        
        # 模拟参数
        is_proxy = 0
        finger_dt = "test_finger"
        mysql = None  # 测试时不使用真实数据库
        
        # 发送请求
        result = zr_details.send_request(test_url, is_proxy, finger_dt, mysql)
        
        if result:
            logger.info("DrissionPage请求成功，返回数据长度: {}".format(len(result)))
            logger.info("页面标题检查...")
            if "自如" in result:
                logger.info("✅ 页面内容正常，包含'自如'关键词")
            else:
                logger.warning("⚠️ 页面内容可能异常，未找到'自如'关键词")
        else:
            logger.error("❌ DrissionPage请求失败")
            
    except Exception as e:
        logger.error("测试失败: {}".format(e))
    finally:
        # 清理资源
        try:
            if hasattr(zr_details, 'page') and zr_details.page:
                zr_details.page.quit()
        except:
            pass

if __name__ == '__main__':
    test_drission_integration()
