# !/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author:   Wlj
Date:     2024-11-05 12:32:22

"""
import time

import requests
import execjs
import re

# from DrissionPage import Chromium, ChromiumOptions
from DrissionPage import Chromium,ChromiumPage, ChromiumOptions


def get_initial_cookies():
    return {
    "__jsluid_s": "637d9d689d1561819dff10b9b1baecb3",
    "sensorsdata2015jssdkcross": "%7B%22distinct_id%22%3A%22192fb27b8a2a3-0f7155f47c68de-26011951-2073600-192fb27b8a3442%22%2C%22%24device_id%22%3A%22192fb27b8a2a3-0f7155f47c68de-26011951-2073600-192fb27b8a3442%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_referrer%22%3A%22%22%2C%22%24latest_referrer_host%22%3A%22%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%7D%7D",
    "Hm_lvt_4f083817a81bcb8eed537963fc1bbf10": "1744635512",
    "__jsl_clearance_s": "1745244643.048|0|cJmjlFseZ8tp5t5XsMldGmR1Gqw%3D",
    "CURRENT_CITY_CODE": "420100",
    "_csrf": "AKkpJABzWp2ZVpxb-ctah91YS4YP_Q06",
    "Hm_lpvt_4f083817a81bcb8eed537963fc1bbf10": "1745244647"
}


def get_cookies():
    cookies_dict = get_initial_cookies()
    try:
        co = ChromiumOptions()
        # co.headless()  # 无头模式
        # co.set_argument('--headless=new')
        # co.set_argument('--no-sandbox')  # 无沙盒模式
        co.set_user_agent(
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
        page = ChromiumPage(co)
        tab = page.latest_tab
        tab.get('https://wh.ziroom.com/z/')
        time.sleep(3)
        tab.refresh()
        time.sleep(3)
        new_cookies_dict = tab.cookies(all_info=True).as_dict()
        _user_agent = tab.user_agent
        return new_cookies_dict, _user_agent
    except Exception as e:
        print(f"Error in get_cookies: {e}")


headers = {
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Accept-Encoding": "gzip, deflate, br, zstd",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "Cache-Control": "no-cache",
    "Connection": "keep-alive",
    # "Cookie": "__jsluid_s=e7e76a2e023d0b25318128555e1dfb4c; __jsl_clearance_s=**********.359|0|uAQzsvh49i%2FgI8uYshryxm7MGSc%3D; CURRENT_CITY_CODE=420100; _csrf=FvdJ9qThLAb5rPmEq7E_j1FYUUYE3D2I; sajssdk_2015_cross_new_user=1; Hm_lvt_4f083817a81bcb8eed537963fc1bbf10=**********; HMACCOUNT=****************; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%221963489a82b5-099714d83fc3e4-26011c51-2073600-1963489a82c249%22%2C%22%24device_id%22%3A%221963489a82b5-099714d83fc3e4-26011c51-2073600-1963489a82c249%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_referrer%22%3A%22%22%2C%22%24latest_referrer_host%22%3A%22%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%7D%7D; Hm_lpvt_4f083817a81bcb8eed537963fc1bbf10=**********",
    "Host": "wh.ziroom.com",
    "Pragma": "no-cache",
    "Sec-Fetch-Dest": "document",
    "Sec-Fetch-Mode": "navigate",
    "Sec-Fetch-Site": "none",
    "Sec-Fetch-User": "?1",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "sec-ch-ua": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\""
}
ck, ua = get_cookies()
r = requests.get('https://wh.ziroom.com/z/', headers=headers, cookies=ck)
print(r)
print(r.text)
